using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Exceptions;

namespace EAMS.Domain.Services;

public class AccommodationService : IAccommodationService
{
    private readonly IAccommodationRepository _accommodationRepository;

    public AccommodationService(IAccommodationRepository accommodationRepository)
    {
        _accommodationRepository = accommodationRepository;
    }

    public async Task<IEnumerable<Accommodation>> GetAll(
        string? search = null,
        AccommodationType? accommodationType = null,
        Density? density = null,
        Duration? duration = null,
        Region? region = null,
        bool? inactive = null)
    {
        // If no filters are provided, return all accommodations
        if (string.IsNullOrWhiteSpace(search) &&
            accommodationType == null &&
            density == null &&
            duration == null &&
            region == null &&
            inactive == null)
        {
            return await _accommodationRepository.GetAllAsync();
        }

        // Build the filter expression
        return await _accommodationRepository.GetAllAsync(a =>
            // Search term filter (OR logic within search fields)
            (string.IsNullOrWhiteSpace(search) ||
             a.Name.ToLower().Contains(search.ToLower()) ||
             a.StreetLine1.ToLower().Contains(search.ToLower()) ||
             (a.StreetLine2 != null && a.StreetLine2.ToLower().Contains(search.ToLower())) ||
             a.Suburb.ToLower().Contains(search.ToLower()) ||
             a.Postcode.ToLower().Contains(search.ToLower())) &&

            // Accommodation type filter
            (accommodationType == null || a.AccommodationType == accommodationType) &&

            // Density filter
            (density == null || a.Density == density) &&

            // Duration filter
            (duration == null || a.Duration.Contains(duration)) &&

            // Region filter
            (region == null || a.Region == region) &&

            // Inactive status filter
            (inactive == null || a.Inactive == inactive)
        );
    }

    public async Task<Accommodation?> GetById(Int64 id)
    {
        return await _accommodationRepository.GetByIdAsync(id);
    }

    public async Task<Accommodation> Create(Accommodation accommodation)
    {
        // Set timestamps for new entity
        accommodation.CreatedAt = DateTime.UtcNow;
        accommodation.UpdatedAt = DateTime.UtcNow;

        // AddAsync returns void and handles SaveChanges internally
        await _accommodationRepository.AddAsync(accommodation);

        // Return the accommodation with its generated ID
        return accommodation;
    }

    public async Task<Accommodation> Update(Accommodation accommodation)
    {
        // Check if accommodation exists first
        var existingAccommodation = await _accommodationRepository.GetByIdAsync(accommodation.Id);
        if (existingAccommodation == null)
        {
            throw new EntityNotFoundException("Accommodation", accommodation.Id);
        }

        // Update timestamp
        accommodation.UpdatedAt = DateTime.UtcNow;
        // Preserve original creation timestamp
        accommodation.CreatedAt = existingAccommodation.CreatedAt;

        // UpdateAsync returns void and handles SaveChanges internally
        await _accommodationRepository.UpdateAsync(accommodation);

        return accommodation;
    }

    public async Task<bool> Delete(Int64 id)
    {
        // Check if accommodation exists first
        var exists = await _accommodationRepository.GetByIdAsync(id);
        if (exists == null)
        {
            return false;
        }

        // DeleteAsync returns void and handles SaveChanges internally
        await _accommodationRepository.DeleteAsync(id);

        return true;
    }
}
