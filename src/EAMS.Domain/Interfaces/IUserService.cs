﻿using EAMS.Domain.Entities;
using GraphUser = Microsoft.Graph.Models.User;


namespace EAMS.Domain.Interfaces;

public interface IUserService
{
    public Task<User?> GetCurrentLoginUserAsync();
    public Task<User?> GetUserByIdAsync(Guid userId);
    public Task<User?> GetUserByEmailAsync(string email);
    public Task<User?> UpdateUserDetailsAsync(GraphUser graphUser);
    public Task<UserInvitation?> CreateInvitationAsync(UserInvitation userInvitation);
    public Task<(List<User> results, int totalCount)> SearchUsersAsync(
        string? searchTerm, string? searchProperty,
        int? pageNumber, int? pageSize, string sortBy, string sortDirection);
    public Task AddUserToGroupAsync(Guid userId, string groupName);
    public Task RemoveUserFromGroupAsync(Guid userId, string groupName);
    public Task DeleteUserAsync(Guid userId);
    public Task<List<User>> GetUsersByOrganisationIdsAsync(List<Guid> orgIds);
    public Task<List<User>> GetAllUsersAsync();
}
