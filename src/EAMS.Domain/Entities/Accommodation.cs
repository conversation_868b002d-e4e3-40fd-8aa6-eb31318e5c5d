namespace EAMS.Domain.Entities;

public class Accommodation : SoftDeletableEntity<Int64>
{
    public string Name { get; set; } = string.Empty;

    public string StreetLine1 { get; set; } = string.Empty;

    public string? StreetLine2 { get; set; }

    public string Suburb { get; set; } = string.Empty;

    public string State { get; set; } = string.Empty;

    public string Postcode { get; set; } = string.Empty;

    public int RegionId {  get; set; }

    public string? Phone { get; set; }

    public string? Email { get; set; }

    public string? Website { get; set; }

    public int AccommodationTypeId { get; set; }
    public int DensityId { get; set; }

    public AccommodationType AccommodationType { get; set; }

    public Region Region { get; set; }
    public Density Density { get; set; }

    public List<Duration> Duration { get; set; } = new();

    public bool Inactive { get; set; } = false;

    // Navigation properties for relationships
    public ICollection<Organisation> Organisations { get; set; } = new List<Organisation>();
    public ICollection<AmenityOptions> AmenityOptions { get; set; } = new List<AmenityOptions>();

    public GeoPoint? Location { get; set; }

    public void SetLocation(GeoPoint? location) => Location = location;
}

public readonly record struct GeoPoint(double Latitude, double Longitude)
{
    public static GeoPoint Create(double latitude, double longitude)
    {
        return new GeoPoint(latitude, longitude);
    }
}