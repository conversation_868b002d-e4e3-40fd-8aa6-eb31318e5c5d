namespace EAMS.Domain.Entities;

public abstract class EnumEntity : SoftDeletableEntity<int>
{
    public string Name { get; set; } = null;
}

public class AccommodationType : EnumEntity
{
    public ICollection<Accommodation> Accommodations { get; set; }
}
public class Density : EnumEntity
{
    public ICollection<Accommodation> Accommodations { get; set; }
}

public class Region : EnumEntity
{
    public ICollection<Accommodation> Accommodations { get; set; }
}

public class Duration : EnumEntity{
    public ICollection<Accommodation> Accommodations { get; set; }
}

public class AmenityType : EnumEntity
{
    public ICollection<Amenity> Amenities { get; set; }
}
