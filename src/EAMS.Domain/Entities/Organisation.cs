namespace EAMS.Domain.Entities;

public class Organisation : SoftDeletableEntity<Guid>
{
    public string Name { get; set; }
    public Guid? ParentOrgId { get; set; }
    public string StreetLine1 { get; set; }
    public string? StreetLine2 { get; set; }
    public string State { get; set; }
    public string Postcode { get; set; }
    public string Suburb { get; set; }
    public string? Service { get; set; }
    public string? ClientGroup { get; set; }
    public string? KeyContactName { get; set; }
    public string? KeyContactPhone { get; set; }
    public string? KeyContactEmail { get; set; }

    // Navigation properties for relationships
    public Organisation ParentOrg { get; set; }
    public ICollection<Organisation> ChildOrganisations { get; set; } = new List<Organisation>();
    public ICollection<User> Users { get; set; } = new List<User>();
    public ICollection<Accommodation> Accommodations { get; set; } = new List<Accommodation>();
    public ICollection<UserInvitation> UserInvitations { get; set; } = new List<UserInvitation>();
}