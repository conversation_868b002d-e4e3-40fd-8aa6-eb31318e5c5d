﻿namespace EAMS.Domain.Entities;

public class Amenity : BaseEntity<Int64>
{
    public string Name { get; set; }
    public int AmenityTypeId { get; set; }
    public AmenityType AmenityType { get; set; }
    public string HelpText { get; set; }
    // Navigation properties for relationships
    public ICollection<AmenityOptions> AmenityOptions { get; set; } = new List<AmenityOptions>();
}

public class AmenityOptions : BaseEntity<Int64>
{
    public string Name { get; set; }
    public string DisplayText { get; set; }
    public string Icon { get; set; }
    public string Color { get; set; }

    // Foreign key for Amenity relationship
    public Int64 AmenityId { get; set; }

    // Navigation properties for relationships
    public Amenity Amenity { get; set; }
    public ICollection<Accommodation> Accommodations { get; set; } = new List<Accommodation>();

}
