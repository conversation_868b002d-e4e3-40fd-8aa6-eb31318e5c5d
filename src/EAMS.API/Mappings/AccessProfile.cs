using AutoMapper;
using EAMS.API.DTOs;
using EAMS.Domain.Entities;
using GraphUser = Microsoft.Graph.Models.User;

namespace EAMS.API.Mappings;

public class AccessProfile : Profile
{
    public AccessProfile()
    {
        // User mapping
        CreateMap<User, UserDto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.GraphUser.DisplayName))
            .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.GraphUser.Mail))
            .ForMember(dest => dest.GivenName, opt => opt.MapFrom(src => src.GraphUser.GivenName))
            .ForMember(dest => dest.Surname, opt => opt.MapFrom(src => src.GraphUser.Surname))
            .ForMember(dest => dest.UserPrincipalName, opt => opt.MapFrom(src => src.GraphUser.UserPrincipalName))
            .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.Organisation != null ? src.Organisation.Name : src.GraphUser.CompanyName))
            .ForMember(dest => dest.JobTitle, opt => opt.MapFrom(src => src.GraphUser.JobTitle))
            .ForMember(dest => dest.BusinessPhone, opt => opt.MapFrom(src => src.GraphUser.BusinessPhones))
            .ForMember(dest => dest.MobilePhone, opt => opt.MapFrom(src => src.GraphUser.MobilePhone));

        CreateMap<UserDto, GraphUser>()
            .ForMember(dest => dest.Mail, opt => opt.MapFrom(src => src.Email));

        // Invitation mapping
        CreateMap<InvitationDto, UserInvitation>();

        // Organisation mapping
        CreateMap<CreateOrganisationDto, Organisation>();
        CreateMap<UpdateOrganisationDto, Organisation>();
        CreateMap<Organisation, OrganisationDetailsDto>()
            .ForMember(dest => dest.ParentOrganisation, opt => opt.MapFrom(src => src.ParentOrg));
    }
}
