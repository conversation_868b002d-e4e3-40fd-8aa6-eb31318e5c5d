using EAMS.Domain.Entities.Helper;

namespace EAMS.API.DTOs;

public class SearchRequestDto
{
    public string? SearchTerm { get; set; }
    public Pagination Pagination { get; set; }
    public SearchSorting? Sorting { get; set; }

    public virtual bool ValidateQuery()
    {
        if (this.Pagination is null)
            return false;
        else if (this.Pagination.Page <= 0 || this.Pagination.PageSize <= 0) 
            return false;

        return true;
    }
}