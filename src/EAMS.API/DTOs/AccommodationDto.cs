using System.ComponentModel.DataAnnotations;
using EAMS.Domain.Entities;

namespace EAMS.API.DTOs;

public class AccommodationDto
{
    public Int64 Id { get; set; }

    [Required(ErrorMessage = "Name is required")]
    [StringLength(200, ErrorMessage = "Name cannot exceed 200 characters")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "Street is required")]
    [StringLength(200, ErrorMessage = "Street cannot exceed 200 characters")]
    public string StreetLine1 { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "Street cannot exceed 200 characters")]
    public string? StreetLine2 { get; set; }

    [Required(ErrorMessage = "Suburb is required")]
    [StringLength(100, ErrorMessage = "Suburb cannot exceed 100 characters")]
    public string Suburb { get; set; } = string.Empty;

    [Required(ErrorMessage = "State is required")]
    [StringLength(50, ErrorMessage = "State cannot exceed 50 characters")]
    public string State { get; set; } = string.Empty;

    [Required(ErrorMessage = "Postcode is required")]
    [StringLength(10, ErrorMessage = "Postcode cannot exceed 10 characters")]
    [RegularExpression(@"^\d{4}$", ErrorMessage = "Postcode must be 4 digits")]
    public string Postcode { get; set; } = string.Empty;

    [Required(ErrorMessage = "Region is required")]
    [EnumDataType(typeof(Region), ErrorMessage = "Invalid region value")]
    public Region Region { get; set; }

    [Phone(ErrorMessage = "Invalid phone number format")]
    [StringLength(20, ErrorMessage = "Phone cannot exceed 20 characters")]
    public string? Phone { get; set; }

    [EmailAddress(ErrorMessage = "Invalid email format")]
    [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
    public string? Email { get; set; }

    [Url(ErrorMessage = "Invalid website URL format")]
    [StringLength(200, ErrorMessage = "Website cannot exceed 200 characters")]
    public string? Website { get; set; }

    [Required(ErrorMessage = "Accommodation type is required")]
    [EnumDataType(typeof(AccommodationType), ErrorMessage = "Invalid accommodation type value")]
    public AccommodationType AccommodationType { get; set; }

    [Required(ErrorMessage = "Density is required")]
    [EnumDataType(typeof(Density), ErrorMessage = "Invalid density value")]
    public Density Density { get; set; }

    [Required(ErrorMessage = "Duration is required")]
    [MinLength(1, ErrorMessage = "At least one duration is required")]
    public List<Duration> Duration { get; set; } = new();

    public bool Inactive { get; set; } = false;

    public List<Int64> AmenityIds { get; set; } = new();

    [Range(-90, 90, ErrorMessage = "Latitude must be between -90 and 90 degrees")]
    public double? Latitude { get; set; }

    [Range(-180, 180, ErrorMessage = "Longitude must be between -180 and 180 degrees")]
    public double? Longitude { get; set; }
}
