﻿namespace EAMS.API.DTOs
{
    public class OrganisationDetailsDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string StreetLine1 { get; set; }
        public string? StreetLine2 { get; set; }
        public string State { get; set; }
        public string Postcode { get; set; }
        public string Suburb { get; set; }
        public string? Service { get; set; }
        public string? ClientGroup { get; set; }
        public string? KeyContactName { get; set; }
        public string? KeyContactPhone { get; set; }
        public string? KeyContactEmail { get; set; }

        public OrganisationDetailsDto? ParentOrganisation { get; set; }
    }
}
