using EAMS.API.Configurations;
using EAMS.API.DTOs;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace EAMS.API.Filters;

/// <summary>
/// Action filter that automatically validates ModelState and returns standardized error responses
/// </summary>
public class ModelStateValidationFilter : ActionFilterAttribute
{
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        if (!context.ModelState.IsValid)
        {
            var correlationIdGenerator = context.HttpContext.RequestServices.GetService<ICorrelationIdGenerator>();
            var correlationId = correlationIdGenerator?.Get() ?? Guid.NewGuid().ToString();

            var validationErrors = context.ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value!.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            var errorResponse = new ErrorResponse
            {
                Message = "One or more validation errors occurred",
                StatusCode = 400,
                CorrelationId = correlationId,
                ValidationErrors = validationErrors
            };

            context.Result = new BadRequestObjectResult(errorResponse);
        }

        base.OnActionExecuting(context);
    }
}
