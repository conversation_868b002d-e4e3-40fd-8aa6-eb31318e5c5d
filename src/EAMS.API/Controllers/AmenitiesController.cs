using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using EAMS.API.DTOs;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Entities;
using EAMS.Domain.Exceptions;
using AutoMapper;

namespace EAMS.API.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class AmenitiesController : ControllerBase
{
    private readonly IAmenityService _amenityService;
    private readonly ILogger<AmenitiesController> _logger;
    private readonly IMapper _mapper;

    public AmenitiesController(
        IAmenityService amenityService,
        ILogger<AmenitiesController> logger,
        IMapper mapper)
    {
        _amenityService = amenityService;
        _logger = logger;
        _mapper = mapper;
    }

    /// <summary>
    /// Get all amenities
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<IEnumerable<AmenityDto>>> GetAmenities()
    {
        var amenities = await _amenityService.GetAll();
        var response = _mapper.Map<IEnumerable<AmenityDto>>(amenities);
        return Ok(response);
    }

    /// <summary>
    /// Get amenity by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<AmenityDto>> GetAmenity(Int64 id)
    {
        var amenity = await _amenityService.GetById(id);

        if (amenity == null)
        {
            throw new EntityNotFoundException("Amenity", id);
        }

        var response = _mapper.Map<AmenityDto>(amenity);
        return Ok(response);
    }

    /// <summary>
    /// Create a new amenity
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<AmenityDto>> CreateAmenity(AmenityDto amenityDto)
    {
        var amenity = _mapper.Map<Amenity>(amenityDto);
        var createdAmenity = await _amenityService.Create(amenity);
        var response = _mapper.Map<AmenityDto>(createdAmenity);

        return CreatedAtAction(nameof(GetAmenity), new { id = createdAmenity.Id }, response);
    }

    /// <summary>
    /// Update an existing amenity
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<AmenityDto>> UpdateAmenity(Int64 id, AmenityDto amenityDto)
    {
        var amenity = _mapper.Map<Amenity>(amenityDto);
        var updatedAmenity = await _amenityService.Update(amenity);
        var response = _mapper.Map<AmenityDto>(updatedAmenity);

        return Ok(response);
    }

    /// <summary>
    /// Delete an amenity
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<bool>> DeleteAmenity(Int64 id)
    {
        var result = await _amenityService.Delete(id);
        return Ok(result);
    }
}
