using AutoMapper;
using EAMS.API.DTOs;
using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace EAMS.API.Controllers
{
    [Route("api/[controller]")]
    public class EnumsController : Controller
    {
        private readonly ILogger<EnumsController> _logger;
        private readonly IRepository<AccommodationType, int> _accommodationTypeRepository;
        private readonly IRepository<AmenityType, int> _amenityTypeRepository;
        private readonly IRepository<Density, int> _densityRepository;
        private readonly IRepository<Duration, int> _durationRepository;
        private readonly IRepository<Region, int> _regionRepository;
        private readonly IMapper _mapper;

        public EnumsController(ILogger<EnumsController> logger,
            IRepository<AccommodationType, int> accommodationTypeRepository,
            IRepository<AmenityType, int> amenityTypeRepository,
            IRepository<Density, int> densityRepository,
            IRepository<Duration, int> durationRepository,
            IRepository<Region, int> regionRepository,
            IMapper mapper
        )
        {
            _logger = logger;
            _accommodationTypeRepository = accommodationTypeRepository;
            _amenityTypeRepository = amenityTypeRepository;
            _densityRepository = densityRepository;
            _durationRepository = durationRepository;
            _regionRepository = regionRepository;
            _mapper = mapper;
        }

        /// <summary>
        /// Get all accommodation types enums
        /// </summary>
        /// <returns></returns>
        [HttpGet("accommodation-types")]
        public async Task<ActionResult<IEnumerable<EnumDto>>> GetAccommodationTypes()
        {
            var accommodationTypes = await _accommodationTypeRepository.GetAllAsync();
            return Ok(_mapper.Map<IEnumerable<EnumDto>>(accommodationTypes.ToList()));
        }

        /// <summary>
        /// Get all amenity types enums
        /// </summary>
        /// <returns></returns>
        [HttpGet("amenity-types")]
        public async Task<ActionResult<IEnumerable<EnumDto>>> GetAmenityTypes()
        {
            var amenityTypes = await _amenityTypeRepository.GetAllAsync();
            return Ok(_mapper.Map<IEnumerable<EnumDto>>(amenityTypes.ToList()));
        }

        /// <summary>
        /// Get all density enums
        /// </summary>
        /// <returns></returns>
        [HttpGet("densities")]
        public async Task<ActionResult<IEnumerable<EnumDto>>> GetDensities()
        {
            var densities = await _densityRepository.GetAllAsync();
            return Ok(_mapper.Map<IEnumerable<EnumDto>>(densities.ToList()));
        }

        /// <summary>
        /// Get all duration enums
        /// </summary>
        /// <returns></returns>
        [HttpGet("durations")]
        public async Task<ActionResult<IEnumerable<EnumDto>>> GetDurations()
        {
            var durations = await _durationRepository.GetAllAsync();
            return Ok(_mapper.Map<IEnumerable<EnumDto>>(durations.ToList()));
        }

        /// <summary>
        /// Get all region enums
        /// </summary>
        /// <returns></returns>
        [HttpGet("regions")]
        public async Task<ActionResult<IEnumerable<EnumDto>>> GetRegions()
        {
            var regions = await _regionRepository.GetAllAsync();
            return Ok(_mapper.Map<IEnumerable<EnumDto>>(regions.ToList()));
        }

        /// <summary>
        /// Create a new accommodation type enum
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPost("accommodation-types")]
        public async Task<ActionResult<EnumDto>> CreateAccommodationType([FromBody] EnumDto dto)
        {
            var accommodationType = new AccommodationType
            {
                Name = dto.Name
            };
            await _accommodationTypeRepository.AddAsync(accommodationType);
            return CreatedAtAction(nameof(GetAccommodationTypes), new { id = accommodationType.Id }, _mapper.Map<EnumDto>(accommodationType));
        }

        /// <summary>
        /// Create a new amenity type enum
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPost("amenity-types")]
        public async Task<ActionResult<EnumDto>> CreateAmenityType([FromBody] EnumDto dto)
        {
            var amenityType = new AmenityType
            {
                Name = dto.Name
            };
            await _amenityTypeRepository.AddAsync(amenityType);
            return CreatedAtAction(nameof(GetAmenityTypes), new { id = amenityType.Id }, _mapper.Map<EnumDto>(amenityType));
        }

        /// <summary>
        /// Create a new density enum
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPost("densities")]
        public async Task<ActionResult<EnumDto>> CreateDensity([FromBody] EnumDto dto)
        {
            var density = new Density
            {
                Name = dto.Name
            };
            await _densityRepository.AddAsync(density);
            return CreatedAtAction(nameof(GetDensities), new { id = density.Id }, _mapper.Map<EnumDto>(density));
        }

        /// <summary>
        /// Create a new duration enum
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPost("durations")]
        public async Task<ActionResult<EnumDto>> CreateDuration([FromBody] EnumDto dto)
        {
            var duration = new Duration
            {
                Name = dto.Name
            };
            await _durationRepository.AddAsync(duration);
            return CreatedAtAction(nameof(GetDurations), new { id = duration.Id }, _mapper.Map<EnumDto>(duration));
        }

        /// <summary>
        /// Create a new region enum
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPost("regions")]
        public async Task<ActionResult<EnumDto>> CreateRegion([FromBody] EnumDto dto)
        {
            var region = new Region
            {
                Name = dto.Name
            };
            await _regionRepository.AddAsync(region);
            return CreatedAtAction(nameof(GetRegions), new { id = region.Id }, _mapper.Map<EnumDto>(region));
        }

        /// <summary>
        /// Update an accommodation type enum
        /// </summary>
        /// <param name="id"></param>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPut("accommodation-types/{id}")]
        public async Task<ActionResult<EnumDto>> UpdateAccommodationType(int id, [FromBody] EnumDto dto)
        {
            var accommodationType = await _accommodationTypeRepository.GetByIdAsync(id);
            if (accommodationType == null)
            {
                return NotFound();
            }
            accommodationType.Name = dto.Name;
            await _accommodationTypeRepository.UpdateAsync(accommodationType);
            return Ok(_mapper.Map<EnumDto>(accommodationType));
        }

        /// <summary>
        /// Update an amenity type enum
        /// </summary>
        /// <param name="id"></param>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPut("amenity-types/{id}")]
        public async Task<ActionResult<EnumDto>> UpdateAmenityType(int id, [FromBody] EnumDto dto)
        {
            var amenityType = await _amenityTypeRepository.GetByIdAsync(id);
            if (amenityType == null)
            {
                return NotFound();
            }
            amenityType.Name = dto.Name;
            await _amenityTypeRepository.UpdateAsync(amenityType);
            return Ok(_mapper.Map<EnumDto>(amenityType));
        }

        /// <summary>
        /// Update a density enum
        /// </summary>
        /// <param name="id"></param>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPut("densities/{id}")]
        public async Task<ActionResult<EnumDto>> UpdateDensity(int id, [FromBody] EnumDto dto)
        {
            var density = await _densityRepository.GetByIdAsync(id);
            if (density == null)
            {
                return NotFound();
            }
            density.Name = dto.Name;
            await _densityRepository.UpdateAsync(density);
            return Ok(_mapper.Map<EnumDto>(density));
        }

        /// <summary>
        /// Update a duration enum
        /// </summary>
        /// <param name="id"></param>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPut("durations/{id}")]
        public async Task<ActionResult<EnumDto>> UpdateDuration(int id, [FromBody] EnumDto dto)
        {
            var duration = await _durationRepository.GetByIdAsync(id);
            if (duration == null)
            {
                return NotFound();
            }
            duration.Name = dto.Name;
            await _durationRepository.UpdateAsync(duration);
            return Ok(_mapper.Map<EnumDto>(duration));
        }

        /// <summary>
        /// Update a region enum
        /// </summary>
        /// <param name="id"></param>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPut("regions/{id}")]
        public async Task<ActionResult<EnumDto>> UpdateRegion(int id, [FromBody] EnumDto dto)
        {
            var region = await _regionRepository.GetByIdAsync(id);
            if (region == null)
            {
                return NotFound();
            }
            region.Name = dto.Name;
            await _regionRepository.UpdateAsync(region);
            return Ok(_mapper.Map<EnumDto>(region));
        }

        /// <summary>
        /// Delete an accommodation type enum
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpDelete("accommodation-types/{id}")]
        public async Task<IActionResult> DeleteAccommodationType(int id)
        {
            var accommodationType = await _accommodationTypeRepository.GetByIdAsync(id);
            if (accommodationType == null)
            {
                return NotFound();
            }
            await _accommodationTypeRepository.DeleteAsync(id);
            return Ok();
        }

        /// <summary>
        /// Delete an amenity type enum
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpDelete("amenity-types/{id}")]
        public async Task<IActionResult> DeleteAmenityType(int id)
        {
            var amenityType = await _amenityTypeRepository.GetByIdAsync(id);
            if (amenityType == null)
            {
                return NotFound();
            }
            await _amenityTypeRepository.DeleteAsync(id);
            return Ok();
        }

        /// <summary>
        /// Delete a density enum
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpDelete("densities/{id}")]
        public async Task<IActionResult> DeleteDensity(int id)
        {
            var density = await _densityRepository.GetByIdAsync(id);
            if (density == null)
            {
                return NotFound();
            }
            await _densityRepository.DeleteAsync(id);
            return Ok();
        }

        /// <summary>
        /// Delete a duration enum
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpDelete("durations/{id}")]
        public async Task<IActionResult> DeleteDuration(int id)
        {
            var duration = await _durationRepository.GetByIdAsync(id);
            if (duration == null)
            {
                return NotFound();
            }
            await _durationRepository.DeleteAsync(id);
            return Ok();
        }

        /// <summary>
        /// Delete a region enum
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpDelete("regions/{id}")]
        public async Task<IActionResult> DeleteRegion(int id)
        {
            var region = await _regionRepository.GetByIdAsync(id);
            if (region == null)
            {
                return NotFound();
            }
            await _regionRepository.DeleteAsync(id);
            return Ok();
        }

    }
}