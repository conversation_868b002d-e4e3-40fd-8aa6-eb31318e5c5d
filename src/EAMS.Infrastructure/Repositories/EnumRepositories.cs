using EAMS.Domain.Entities;
using EAMS.Infrastructure.Data;

namespace EAMS.Infrastructure.Repositories;

public class AccommodationTypeRepository : Repository<AccommodationType, int>
{
    public AccommodationTypeRepository(EamsDbContext context) : base(context)
    {
    }
}

public class AmenityTypeRepository : Repository<AmenityType, int>
{
    public AmenityTypeRepository(EamsDbContext context) : base(context)
    {
    }
}

public class DensityRepository : Repository<Density, int>
{
    public DensityRepository(EamsDbContext context) : base(context)
    {
    }
}

public class DurationRepository : Repository<Duration, int>
{
    public DurationRepository(EamsDbContext context) : base(context)
    {
    }
}

public class RegionRepository : Repository<Region, int>
{
    public RegionRepository(EamsDbContext context) : base(context)
    {
    }
}