using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EAMS.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class ConvertEnumToTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Duration",
                table: "Accommodations");

            migrationBuilder.RenameColumn(
                name: "AmenityType",
                table: "Amenities",
                newName: "AmenityTypeId");

            migrationBuilder.RenameColumn(
                name: "Region",
                table: "Accommodations",
                newName: "RegionId");

            migrationBuilder.RenameColumn(
                name: "Density",
                table: "Accommodations",
                newName: "DensityId");

            migrationBuilder.RenameColumn(
                name: "AccommodationType",
                table: "Accommodations",
                newName: "AccommodationTypeId");

            migrationBuilder.CreateTable(
                name: "AccommodationType",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DiscardedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccommodationType", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "AccommodationType",
                columns: new[] {"Id", "Name", "CreatedAt", "UpdatedAt" },
                values: new object[,]
                {
                    {1, "Hotel", DateTime.UtcNow, DateTime.UtcNow },
                    {2, "ApartmentHotel", DateTime.UtcNow, DateTime.UtcNow },
                    {3, "CaravanPark", DateTime.UtcNow, DateTime.UtcNow },
                    {4, "Homestay", DateTime.UtcNow, DateTime.UtcNow },
                    {5, "Hostel", DateTime.UtcNow, DateTime.UtcNow },
                    {6, "MotelOrMotorInn", DateTime.UtcNow, DateTime.UtcNow },
                    {7, "RoomingHouse", DateTime.UtcNow, DateTime.UtcNow },
                    {8, "RoomingHouseUnregistered", DateTime.UtcNow, DateTime.UtcNow },
                    {9, "ServicedApartment", DateTime.UtcNow, DateTime.UtcNow },
                    {10, "SingleUnit", DateTime.UtcNow, DateTime.UtcNow },
                    {11, "SRSPensionLevel", DateTime.UtcNow, DateTime.UtcNow },
                    {12, "SRSAbovePensionLevel", DateTime.UtcNow, DateTime.UtcNow }
                }
            );

            migrationBuilder.CreateTable(
                name: "AmenityType",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DiscardedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AmenityType", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "AmenityType",
                columns: new[] { "Id", "Name", "CreatedAt", "UpdatedAt" },
                values: new object[,]
                {
                    {1, "Characteristic", DateTime.UtcNow, DateTime.UtcNow },
                    {2, "Safety", DateTime.UtcNow, DateTime.UtcNow },
                    {3, "Amenity", DateTime.UtcNow, DateTime.UtcNow },
                    {4, "Additional", DateTime.UtcNow, DateTime.UtcNow }
                });

            migrationBuilder.CreateTable(
                name: "Density",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DiscardedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Density", x => x.Id);
                });
            
            migrationBuilder.InsertData(
                table: "Density",
                columns: new[] { "Id", "Name", "CreatedAt", "UpdatedAt" },
                values: new object[,]
                {
                    {1, "Low", DateTime.UtcNow, DateTime.UtcNow },
                    {2, "Medium", DateTime.UtcNow, DateTime.UtcNow },
                    {3, "High", DateTime.UtcNow, DateTime.UtcNow }
                });

            migrationBuilder.CreateTable(
                name: "Duration",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DiscardedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Duration", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "Duration",
                columns: new[] { "Id", "Name", "CreatedAt", "UpdatedAt" },
                values: new object[,]
                {
                    {1, "SingleTermStay", DateTime.UtcNow, DateTime.UtcNow },
                    {2, "ShortTermStay", DateTime.UtcNow, DateTime.UtcNow },
                    {3, "LongTermStay", DateTime.UtcNow, DateTime.UtcNow }
                });
            
            
            migrationBuilder.CreateTable(
                name: "Region",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DiscardedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Region", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "Region",
                columns: new[] { "Id", "Name", "CreatedAt", "UpdatedAt" },
                values: new object[,]
                {
                    {1, "Barwon", DateTime.UtcNow, DateTime.UtcNow },
                    {2, "BaysidePeninsula", DateTime.UtcNow, DateTime.UtcNow },
                    {3, "BrimbankMelton", DateTime.UtcNow, DateTime.UtcNow },
                    {4, "CentralHighlands", DateTime.UtcNow, DateTime.UtcNow },
                    {5, "InnerEasternMelbourne", DateTime.UtcNow, DateTime.UtcNow },
                    {6, "OuterEasternMelbourne", DateTime.UtcNow, DateTime.UtcNow },
                    {7, "InnerGippsland", DateTime.UtcNow, DateTime.UtcNow },
                    {8, "OuterGippsland", DateTime.UtcNow, DateTime.UtcNow },
                    {9, "Goulburn", DateTime.UtcNow, DateTime.UtcNow },
                    {10, "HumeMerriBek", DateTime.UtcNow, DateTime.UtcNow },
                    {11, "Loddon", DateTime.UtcNow, DateTime.UtcNow },
                    {12, "Mallee", DateTime.UtcNow, DateTime.UtcNow },
                    {13, "MelbourneCBD", DateTime.UtcNow, DateTime.UtcNow },
                    {14, "NorthEasternMelbourne", DateTime.UtcNow, DateTime.UtcNow },
                    {15, "OvensMurray", DateTime.UtcNow, DateTime.UtcNow },
                    {16, "SouthernMelbourne", DateTime.UtcNow, DateTime.UtcNow },
                    {17, "OuterWesternDistrict", DateTime.UtcNow, DateTime.UtcNow },
                    {18, "WesternMelbourne", DateTime.UtcNow, DateTime.UtcNow }
                });

            migrationBuilder.CreateTable(
                name: "AccommodationDuration",
                columns: table => new
                {
                    AccommodationsId = table.Column<long>(type: "bigint", nullable: false),
                    DurationId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccommodationDuration", x => new { x.AccommodationsId, x.DurationId });
                    table.ForeignKey(
                        name: "FK_AccommodationDuration_Accommodations_AccommodationsId",
                        column: x => x.AccommodationsId,
                        principalTable: "Accommodations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AccommodationDuration_Duration_DurationId",
                        column: x => x.DurationId,
                        principalTable: "Duration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Amenities_AmenityTypeId",
                table: "Amenities",
                column: "AmenityTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_Accommodations_AccommodationTypeId",
                table: "Accommodations",
                column: "AccommodationTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_Accommodations_DensityId",
                table: "Accommodations",
                column: "DensityId");

            migrationBuilder.CreateIndex(
                name: "IX_Accommodations_RegionId",
                table: "Accommodations",
                column: "RegionId");

            migrationBuilder.CreateIndex(
                name: "IX_AccommodationDuration_DurationId",
                table: "AccommodationDuration",
                column: "DurationId");

            migrationBuilder.AddForeignKey(
                name: "FK_Accommodations_AccommodationType_AccommodationTypeId",
                table: "Accommodations",
                column: "AccommodationTypeId",
                principalTable: "AccommodationType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Accommodations_Density_DensityId",
                table: "Accommodations",
                column: "DensityId",
                principalTable: "Density",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Accommodations_Region_RegionId",
                table: "Accommodations",
                column: "RegionId",
                principalTable: "Region",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Amenities_AmenityType_AmenityTypeId",
                table: "Amenities",
                column: "AmenityTypeId",
                principalTable: "AmenityType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Accommodations_AccommodationType_AccommodationTypeId",
                table: "Accommodations");

            migrationBuilder.DropForeignKey(
                name: "FK_Accommodations_Density_DensityId",
                table: "Accommodations");

            migrationBuilder.DropForeignKey(
                name: "FK_Accommodations_Region_RegionId",
                table: "Accommodations");

            migrationBuilder.DropForeignKey(
                name: "FK_Amenities_AmenityType_AmenityTypeId",
                table: "Amenities");

            migrationBuilder.DropTable(
                name: "AccommodationDuration");

            migrationBuilder.DropTable(
                name: "AccommodationType");

            migrationBuilder.DropTable(
                name: "AmenityType");

            migrationBuilder.DropTable(
                name: "Density");

            migrationBuilder.DropTable(
                name: "Region");

            migrationBuilder.DropTable(
                name: "Duration");

            migrationBuilder.DropIndex(
                name: "IX_Amenities_AmenityTypeId",
                table: "Amenities");

            migrationBuilder.DropIndex(
                name: "IX_Accommodations_AccommodationTypeId",
                table: "Accommodations");

            migrationBuilder.DropIndex(
                name: "IX_Accommodations_DensityId",
                table: "Accommodations");

            migrationBuilder.DropIndex(
                name: "IX_Accommodations_RegionId",
                table: "Accommodations");

            migrationBuilder.RenameColumn(
                name: "AmenityTypeId",
                table: "Amenities",
                newName: "AmenityType");

            migrationBuilder.RenameColumn(
                name: "RegionId",
                table: "Accommodations",
                newName: "Region");

            migrationBuilder.RenameColumn(
                name: "DensityId",
                table: "Accommodations",
                newName: "Density");

            migrationBuilder.RenameColumn(
                name: "AccommodationTypeId",
                table: "Accommodations",
                newName: "AccommodationType");

            migrationBuilder.AddColumn<string>(
                name: "Duration",
                table: "Accommodations",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }
    }
}
